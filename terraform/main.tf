# Entanglement Project - Main Terraform Configuration
# This configuration deploys the complete AWS infrastructure for the Entanglement project

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# Configure the AWS Provider
provider "aws" {
  region = var.aws_region
  
  default_tags {
    tags = {
      Project     = var.project
      Environment = var.environment
      ManagedBy   = "Terraform"
    }
  }
}

# VPC Module
module "vpc" {
  source = "./modules/networking/vpc"
  
  vpc_name             = var.vpc_name
  vpc_cidr             = var.vpc_cidr
  availability_zones   = var.availability_zones
  enable_dns_hostnames = var.enable_dns_hostnames
  enable_dns_support   = var.enable_dns_support
  environment          = var.environment
  project              = var.project
}

# Security Groups Module
module "security_groups" {
  source = "./modules/security/security-groups"
  
  vpc_id           = module.vpc.vpc_id
  environment      = var.environment
  project          = var.project
  allowed_ssh_cidr = var.allowed_ssh_cidr
  app_ports        = var.app_ports
}

# EC2 Module
module "ec2" {
  source = "./modules/compute/ec2"
  
  vpc_id                    = module.vpc.vpc_id
  public_subnet_ids         = module.vpc.public_subnet_ids
  private_app_subnet_ids    = module.vpc.private_app_subnet_ids
  bastion_security_group_id = module.security_groups.bastion_security_group_id
  app_security_group_id     = module.security_groups.app_security_group_id
  
  environment   = var.environment
  project       = var.project
  instance_type = var.instance_type
  ami_id        = var.ami_id
  key_name      = var.key_name
}

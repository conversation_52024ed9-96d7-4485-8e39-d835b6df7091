# Entanglement Project - AWS Infrastructure

This Terraform configuration deploys a complete AWS infrastructure for the Entanglement project, including VPC, security groups, and EC2 instances.

## Architecture Overview

### Network Architecture
- **VPC**: `entanglement-dev-vpc-v1` with CIDR `10.0.0.0/16`
- **Availability Zones**: us-east-1a, us-east-1b
- **Subnets**:
  - Public Subnets: `********/24`, `********/24` (Web/Load Balancer Tier)
  - Private App Subnets: `*********/24`, `*********/24` (Application Tier)
  - Private DB Subnets: `*********/24`, `*********/24` (Database Tier)

### Security Architecture
- **Bastion Host**: Secure SSH gateway in public subnet
- **Application Server**: Isolated in private subnet, accessible only via bastion
- **Security Groups**: Principle of least privilege access

### Compute Resources
- **Bastion Host**: `baiker-dev-ec2-bastion-host` (t2.micro, Ubuntu 24.04)
- **Application Server**: `baiker-dev-ec2-app` (t2.micro, Ubuntu 24.04)

## Prerequisites

1. **AWS CLI configured** with appropriate credentials
2. **Terraform installed** (version >= 1.0)
3. **AWS Key Pair** created in us-east-1 region
4. **Your public IP address** for SSH access

## Deployment Instructions

### 1. Clone and Navigate
```bash
cd terraform
```

### 2. Configure Variables
```bash
cp terraform.tfvars.example terraform.tfvars
```

Edit `terraform.tfvars` and update:
- `key_name`: Your AWS key pair name
- `allowed_ssh_cidr`: Your public IP address (get from https://whatismyipaddress.com/)

### 3. Initialize Terraform
```bash
terraform init
```

### 4. Plan Deployment
```bash
terraform plan
```

### 5. Deploy Infrastructure
```bash
terraform apply
```

Type `yes` when prompted to confirm deployment.

### 6. Get Connection Information
After deployment, Terraform will output connection commands:
```bash
terraform output ssh_bastion_command
terraform output ssh_app_command
```

## Usage

### SSH Access Pattern
1. **Connect to Bastion Host**:
   ```bash
   ssh ubuntu@<bastion_public_ip>
   ```

2. **From Bastion, Connect to App Server**:
   ```bash
   ssh ubuntu@<app_private_ip>
   ```

### Application Ports
The application server has the following ports open:
- **1997**: Application port 1
- **1982**: Application port 2
- **9999**: Application port 3

⚠️ **Security Note**: These ports are currently open to `0.0.0.0/0`. After Load Balancer deployment, restrict access to Load Balancer Security Group only.

## Infrastructure Components

### Modules Used
- `modules/networking/vpc`: VPC, subnets, gateways, routing
- `modules/security/security-groups`: Security groups for all components
- `modules/compute/ec2`: EC2 instances with proper configuration

### Cost Optimization
- **t2.micro instances**: Eligible for AWS Free Tier
- **Single NAT Gateway**: Reduces costs while maintaining functionality
- **Minimal resources**: Only essential infrastructure deployed

## Cleanup

To destroy the infrastructure:
```bash
terraform destroy
```

Type `yes` when prompted to confirm destruction.

## Security Considerations

- **Bastion Host Pattern**: Secure access to private resources
- **Network Segmentation**: Clear separation between tiers
- **Principle of Least Privilege**: Minimal required access
- **Private Subnets**: Application and database tiers isolated from internet

## Next Steps

1. **Deploy Load Balancer**: Add load balancer module for high availability
2. **Restrict Security Groups**: Update app server security groups to only allow LB traffic
3. **Add Monitoring**: Implement CloudWatch monitoring and alerting
4. **Database Setup**: Deploy RDS in private database subnets
5. **SSL/TLS**: Configure HTTPS with ACM certificates

## Support

For issues or questions, refer to the module-specific README files in the `modules/` directory.

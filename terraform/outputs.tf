# Entanglement Project - Outputs

# VPC Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = module.vpc.vpc_id
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = module.vpc.vpc_cidr_block
}

output "public_subnet_ids" {
  description = "IDs of the public subnets"
  value       = module.vpc.public_subnet_ids
}

output "private_app_subnet_ids" {
  description = "IDs of the private application subnets"
  value       = module.vpc.private_app_subnet_ids
}

output "private_db_subnet_ids" {
  description = "IDs of the private database subnets"
  value       = module.vpc.private_db_subnet_ids
}

# Security Group Outputs
output "bastion_security_group_id" {
  description = "ID of the bastion host security group"
  value       = module.security_groups.bastion_security_group_id
}

output "app_security_group_id" {
  description = "ID of the application server security group"
  value       = module.security_groups.app_security_group_id
}

output "load_balancer_security_group_id" {
  description = "ID of the load balancer security group"
  value       = module.security_groups.load_balancer_security_group_id
}

# EC2 Outputs
output "bastion_instance_id" {
  description = "ID of the bastion host instance"
  value       = module.ec2.bastion_instance_id
}

output "bastion_public_ip" {
  description = "Public IP address of the bastion host"
  value       = module.ec2.bastion_public_ip
}

output "bastion_private_ip" {
  description = "Private IP address of the bastion host"
  value       = module.ec2.bastion_private_ip
}

output "app_instance_id" {
  description = "ID of the application server instance"
  value       = module.ec2.app_instance_id
}

output "app_private_ip" {
  description = "Private IP address of the application server"
  value       = module.ec2.app_private_ip
}

# Connection Information
output "ssh_bastion_command" {
  description = "SSH command to connect to bastion host"
  value       = "ssh ubuntu@${module.ec2.bastion_public_ip}"
}

output "ssh_app_command" {
  description = "SSH command to connect to app server (from bastion)"
  value       = "ssh ubuntu@${module.ec2.app_private_ip}"
}

# Infrastructure Summary
output "infrastructure_summary" {
  description = "Summary of deployed infrastructure"
  value = {
    vpc_name                = var.vpc_name
    vpc_cidr               = var.vpc_cidr
    availability_zones     = var.availability_zones
    bastion_public_ip      = module.ec2.bastion_public_ip
    app_private_ip         = module.ec2.app_private_ip
    nat_gateway_id         = module.vpc.nat_gateway_id
    internet_gateway_id    = module.vpc.internet_gateway_id
  }
}

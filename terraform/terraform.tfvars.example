# Entanglement Project - Example Terraform Variables
# Copy this file to terraform.tfvars and customize the values

# AWS Configuration
aws_region = "us-east-1"

# Project Configuration
project     = "baiker"
environment = "dev"

# VPC Configuration
vpc_name           = "entanglement-dev-vpc-v1"
vpc_cidr           = "10.0.0.0/16"
availability_zones = ["us-east-1a", "us-east-1b"]

# Security Configuration
allowed_ssh_cidr = "*************/32"  # Replace with your IP address
app_ports        = [1997, 1982, 9999]

# EC2 Configuration
instance_type = "t2.micro"
ami_id        = "ami-020cba7c55df1f615"  # Ubuntu 24.04 LTS in us-east-1
key_name      = "your-key-pair-name"     # Replace with your AWS key pair name

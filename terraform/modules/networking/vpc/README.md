# VPC Module

This module creates a complete VPC infrastructure with multi-tier subnet architecture for the Entanglement project.

## Features

- **VPC**: Creates a VPC with DNS hostnames and resolution enabled
- **Internet Gateway**: Provides internet access for public subnets
- **NAT Gateway**: Enables outbound internet access for private subnets
- **Multi-Tier Subnets**:
  - Public subnets for web/load balancer tier
  - Private subnets for application tier
  - Private subnets for database tier
- **Route Tables**: Properly configured routing for public and private subnets
- **High Availability**: Resources distributed across 2 availability zones

## Architecture

### Subnet Layout
- **Public Subnets**: `********/24` (us-east-1a), `********/24` (us-east-1b)
- **Private App Subnets**: `*********/24` (us-east-1a), `*********/24` (us-east-1b)
- **Private DB Subnets**: `*********/24` (us-east-1a), `*********/24` (us-east-1b)

### Security Design
- **Defense in Depth**: Multiple security layers
- **Network Segmentation**: Clear separation between tiers
- **Controlled Internet Access**: NAT Gateway for private subnet outbound traffic

## Usage

```hcl
module "vpc" {
  source = "./modules/networking/vpc"

  vpc_name             = "entanglement-dev-vpc-v1"
  vpc_cidr             = "10.0.0.0/16"
  availability_zones   = ["us-east-1a", "us-east-1b"]
  environment          = "dev"
  project              = "entanglement"
}
```

## Outputs

- `vpc_id`: VPC identifier
- `public_subnet_ids`: List of public subnet IDs
- `private_app_subnet_ids`: List of private application subnet IDs
- `private_db_subnet_ids`: List of private database subnet IDs
- `nat_gateway_id`: NAT Gateway identifier
- `internet_gateway_id`: Internet Gateway identifier

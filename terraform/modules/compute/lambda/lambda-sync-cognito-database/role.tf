data "aws_iam_policy_document" "lambda_assume_role" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

data "aws_iam_policy_document" "lambda_policy" {
  statement {
    sid = "BasicOps"
    effect = "Allow"
    resources = [
      "*"
    ]
    actions = [
      # "kms:Decrypt",
      # "kms:GenerateDataKey",
      # "ssm:Get*",
      # "ssm:List*",
      # "ssm:Describe*",
      "s3:Get*",
      "s3:List*",
      "s3:PutObject",
      "logs:CreateLogStream",
      "logs:CreateLogGroup",
      "logs:PutLogEvents",
      "ec2:CreateNetworkInterface",
      "ec2:DescribeNetworkInterfaces",
      "ec2:DeleteNetworkInterface"
    ]
  }
}
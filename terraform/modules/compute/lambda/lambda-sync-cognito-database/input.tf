variable "aws_region" {
  type        = string
  description = "AWS region"
}

variable "deployment" {
  type        = string
  description = "Deployment name"
}

variable "environment" {
  type        = string
  description = "Deployment mode"
}

variable "vpc_id" {
  type        = string
  description = "The VPC ID"
}

variable "common_tags" {
  type        = map(string)
  description = "Common tags to use for each resource"
  default     = {}
}

variable "lambda_deployment_path" {
  type        = string
  description = "Required deployment path to find ZIP files"
  default = "terraform/lambda/lambda-sync-cognito-database"
}

variable "memory_size" {
  type        = number
  description = "memory size"
  default     = 128
}

variable "timeout" {
  type        = number
  description = "timeout of lambda function"
  default     = 30
}

variable "function_name" {
  type        = string
  description = "lambda function"
}

variable "package_name" {
  type        = string
  description = "name of the zip file for lambda function"
  default = "deployment"
}

variable  "lambda_extra_paths" {
  type        = string
  description = "PYTHONPATH suffix for additional paths"
  default     = ""
}

variable "lambda_handler" {
  type        = string
  description = "path to lambda function handler"
  default = "lambda_function.lambda_handler"
}

variable "db_host" {
  type        = string
  description = "db host"
}

variable "db_port" {
  type        = string
  description = "db port"
} 

variable "db_user" {
  type        = string
  description = "db user"
} 

variable "db_password" {
  type        = string
  description = "db password"
}  

variable "db_name" {
  type        = string
  description = "db name"
}  








# EC2 Module

This module creates EC2 instances for the Entanglement project infrastructure.

## Features

- **Bastion Host**: Secure SSH gateway in public subnet
- **Application Server**: Main application server in private subnet
- **Automated Setup**: User data scripts for initial configuration
- **Security**: Proper security group assignments and network placement

## Instances Created

### Bastion Host (`baiker-dev-ec2-bastion-host`)
- **Instance Type**: t2.micro
- **AMI**: Ubuntu 24.04 LTS (ami-020cba7c55df1f615)
- **Subnet**: Public Subnet 1 (us-east-1a)
- **Public IP**: Yes
- **Purpose**: Secure SSH gateway for accessing private instances

### Application Server (`baiker-dev-ec2-app`)
- **Instance Type**: t2.micro
- **AMI**: Ubuntu 24.04 LTS (ami-020cba7c55df1f615)
- **Subnet**: Private App Subnet 1 (us-east-1a)
- **Public IP**: No
- **Purpose**: Main application server in isolated environment

## Security Configuration

- **Bastion Host**: SSH access from specific IP (*************/32)
- **Application Server**: SSH access only from bastion host
- **Network Isolation**: Application server has no direct internet access

## User Data Scripts

Both instances include automated setup scripts that:
- Update system packages
- Install essential tools (htop, curl, wget, unzip)
- Configure Docker on application server
- Create informative MOTD messages

## Usage

```hcl
module "ec2" {
  source = "./modules/compute/ec2"

  vpc_id                    = module.vpc.vpc_id
  public_subnet_ids         = module.vpc.public_subnet_ids
  private_app_subnet_ids    = module.vpc.private_app_subnet_ids
  bastion_security_group_id = module.security_groups.bastion_security_group_id
  app_security_group_id     = module.security_groups.app_security_group_id

  environment   = "dev"
  project       = "baiker"
  instance_type = "t2.micro"
  key_name      = "your-key-pair-name"
}
```

## SSH Access Pattern

1. SSH to bastion host: `ssh ubuntu@<bastion_public_ip>`
2. From bastion, SSH to app server: `ssh ubuntu@<app_private_ip>`

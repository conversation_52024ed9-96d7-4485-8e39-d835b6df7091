# EC2 Module - Main Configuration

# Bastion Host Instance
resource "aws_instance" "bastion" {
  ami                    = var.ami_id
  instance_type          = var.instance_type
  key_name              = var.key_name
  subnet_id             = var.public_subnet_ids[0]  # Deploy in first public subnet (us-east-1a)
  vpc_security_group_ids = [var.bastion_security_group_id]

  # Enable public IP assignment
  associate_public_ip_address = true

  # User data script for initial setup
  user_data = base64encode(<<-EOF
    #!/bin/bash
    apt-get update
    apt-get install -y htop curl wget unzip

    # Create a welcome message
    echo "Welcome to Baiker Dev Bastion Host" > /etc/motd
    echo "Instance: $(curl -s http://***************/latest/meta-data/instance-id)" >> /etc/motd
    echo "AZ: $(curl -s http://***************/latest/meta-data/placement/availability-zone)" >> /etc/motd
  EOF
  )

  tags = {
    Name        = "${var.project}-${var.environment}-ec2-bastion-host"
    Environment = var.environment
    Project     = var.project
    Purpose     = "Bastion Host"
    Tier        = "Public"
  }
}

# Application Server Instance
resource "aws_instance" "app" {
  ami                    = var.ami_id
  instance_type          = var.instance_type
  key_name              = var.key_name
  subnet_id             = var.private_app_subnet_ids[0]  # Deploy in first private app subnet (us-east-1a)
  vpc_security_group_ids = [var.app_security_group_id]

  # No public IP for private instance
  associate_public_ip_address = false

  # User data script for initial setup
  user_data = base64encode(<<-EOF
    #!/bin/bash
    apt-get update
    apt-get install -y htop curl wget unzip docker.io

    # Start and enable Docker
    systemctl start docker
    systemctl enable docker

    # Add ubuntu user to docker group
    usermod -aG docker ubuntu

    # Create a welcome message
    echo "Welcome to Baiker Dev Application Server" > /etc/motd
    echo "Instance: $(curl -s http://***************/latest/meta-data/instance-id)" >> /etc/motd
    echo "AZ: $(curl -s http://***************/latest/meta-data/placement/availability-zone)" >> /etc/motd
    echo "Private IP: $(curl -s http://***************/latest/meta-data/local-ipv4)" >> /etc/motd
  EOF
  )

  tags = {
    Name        = "${var.project}-${var.environment}-ec2-app"
    Environment = var.environment
    Project     = var.project
    Purpose     = "Application Server"
    Tier        = "Private"
  }
}

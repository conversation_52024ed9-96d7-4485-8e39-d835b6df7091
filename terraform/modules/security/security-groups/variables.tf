variable "vpc_id" {
  description = "ID of the VPC"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "dev"
}

variable "project" {
  description = "Project name"
  type        = string
  default     = "baiker"
}

variable "allowed_ssh_cidr" {
  description = "CIDR block allowed for SSH access to bastion host"
  type        = string
  default     = "*************/32"
}

variable "app_ports" {
  description = "List of application ports to allow"
  type        = list(number)
  default     = [1997, 1982, 9999]
}

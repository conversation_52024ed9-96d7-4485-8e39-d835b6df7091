output "bastion_security_group_id" {
  description = "ID of the bastion host security group"
  value       = aws_security_group.bastion.id
}

output "app_security_group_id" {
  description = "ID of the application server security group"
  value       = aws_security_group.app.id
}

output "load_balancer_security_group_id" {
  description = "ID of the load balancer security group"
  value       = aws_security_group.load_balancer.id
}

output "bastion_security_group_name" {
  description = "Name of the bastion host security group"
  value       = aws_security_group.bastion.name
}

output "app_security_group_name" {
  description = "Name of the application server security group"
  value       = aws_security_group.app.name
}

output "load_balancer_security_group_name" {
  description = "Name of the load balancer security group"
  value       = aws_security_group.load_balancer.name
}

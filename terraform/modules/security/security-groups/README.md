# Security Groups Module

This module creates security groups for the Entanglement project infrastructure.

## Features

- **Bastion Host Security Group**: Allows SSH access from specific IP address
- **Application Server Security Group**: Allows SSH from bastion and application ports
- **Load Balancer Security Group**: Allows HTTP/HTTPS traffic from internet

## Security Groups Created

### Bastion Host Security Group (`baiker-dev-sg-bastion`)
- **Inbound**: SSH (22) from `*************/32`
- **Outbound**: All traffic to `0.0.0.0/0`

### Application Server Security Group (`baiker-dev-sg-app`)
- **Inbound**:
  - SSH (22) from Bastion Security Group
  - Application ports (1997, 1982, 9999) from `0.0.0.0/0` (temporary)
- **Outbound**: All traffic to `0.0.0.0/0`

### Load Balancer Security Group (`baiker-dev-sg-lb`)
- **Inbound**: HTTP (80) and HTTPS (443) from `0.0.0.0/0`
- **Outbound**: All traffic to `0.0.0.0/0`

## Security Notes

⚠️ **Important**: Application ports are currently open to `0.0.0.0/0` for initial deployment. After Load Balancer deployment, these rules should be updated to only allow traffic from the Load Balancer Security Group.

## Usage

```hcl
module "security_groups" {
  source = "./modules/security/security-groups"

  vpc_id           = module.vpc.vpc_id
  environment      = "dev"
  project          = "baiker"
  allowed_ssh_cidr = "*************/32"
  app_ports        = [1997, 1982, 9999]
}
```

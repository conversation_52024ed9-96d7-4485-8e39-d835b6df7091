# Security Groups Module - Main Configuration

# Security Group for Bastion Host
resource "aws_security_group" "bastion" {
  name        = "${var.project}-${var.environment}-sg-bastion"
  description = "Security group for bastion host"
  vpc_id      = var.vpc_id

  # Inbound Rules
  ingress {
    description = "SSH from specific IP"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = [var.allowed_ssh_cidr]
  }

  # Outbound Rules
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "${var.project}-${var.environment}-sg-bastion"
    Environment = var.environment
    Project     = var.project
    Purpose     = "Bastion Host Security Group"
  }
}

# Security Group for Application Server
resource "aws_security_group" "app" {
  name        = "${var.project}-${var.environment}-sg-app"
  description = "Security group for application server"
  vpc_id      = var.vpc_id

  # SSH access from bastion host
  ingress {
    description     = "SSH from bastion host"
    from_port       = 22
    to_port         = 22
    protocol        = "tcp"
    security_groups = [aws_security_group.bastion.id]
  }

  # Outbound Rules
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "${var.project}-${var.environment}-sg-app"
    Environment = var.environment
    Project     = var.project
    Purpose     = "Application Server Security Group"
  }
}

# Security Group Rules for Application Ports
# Note: These rules allow access from anywhere (0.0.0.0/0) temporarily
# In production, these should be restricted to Load Balancer Security Group
resource "aws_security_group_rule" "app_ports" {
  count             = length(var.app_ports)
  type              = "ingress"
  from_port         = var.app_ports[count.index]
  to_port           = var.app_ports[count.index]
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]  # TODO: Restrict to Load Balancer SG after LB deployment
  security_group_id = aws_security_group.app.id
  description       = "Application port ${var.app_ports[count.index]} - temporary open access"
}

# Security Group for Load Balancer (placeholder for future use)
resource "aws_security_group" "load_balancer" {
  name        = "${var.project}-${var.environment}-sg-lb"
  description = "Security group for load balancer"
  vpc_id      = var.vpc_id

  # HTTP access
  ingress {
    description = "HTTP"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # HTTPS access
  ingress {
    description = "HTTPS"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Outbound Rules
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "${var.project}-${var.environment}-sg-lb"
    Environment = var.environment
    Project     = var.project
    Purpose     = "Load Balancer Security Group"
  }
}

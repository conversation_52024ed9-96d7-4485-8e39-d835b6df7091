locals {
  lambda_name = "${var.deployment}-${var.function_name}-${var.environment}"
}

data "aws_vpc" "selected" {
  id = var.vpc_id
}

data "aws_subnets" "subnets" {
  filter {
    name = "tag:Name"
    values = ["*rivate*"]
  }
  filter {
    name = "vpc-id"
    values = [data.aws_vpc.selected.id]
  }
}

data "aws_security_groups" "default" {
  filter {
    name    = "group-name"
    values  = ["default"]
  }
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.selected.id]
  }
}

resource "aws_iam_role" "lambda_role" {
  name                = "LambaRole@${local.lambda_name}"
  description         = "Lambda assume role: ${local.lambda_name}"
  assume_role_policy  = data.aws_iam_policy_document.lambda_assume_role.json
  tags = merge(
    {
      Name    = "LambaRole@${local.lambda_name}"
    },
    var.common_tags
  )
}

resource "aws_iam_policy" "lambda_policy" {
  name                = "LambaPolicy@${local.lambda_name}"
  description         = "Lamba policy doc: ${local.lambda_name}"
  policy              = data.aws_iam_policy_document.lambda_policy.json
}

resource "aws_iam_role_policy_attachment" "attach_policy" {
  role        = aws_iam_role.lambda_role.name
  policy_arn  = aws_iam_policy.lambda_policy.arn
}

resource "aws_lambda_function" "lambda" {
  filename      = "${var.lambda_deployment_path}/${var.package_name}.zip"
  function_name = local.lambda_name
  description   = local.lambda_name
  role          = aws_iam_role.lambda_role.arn
  handler       = var.lambda_handler
  memory_size   = var.memory_size
  timeout       = var.timeout
  source_code_hash = filebase64sha256("${var.lambda_deployment_path}/${var.package_name}.zip")
  runtime = "python3.9"

  environment {
    variables = {
      DB_HOST = var.db_host
      DB_PORT = var.db_port
      DB_USER = var.db_user
      DB_PASSWORD = var.db_password
      DB_NAME = var.db_name
    }
  }

  vpc_config {
    subnet_ids         = data.aws_subnets.subnets.ids
    security_group_ids = data.aws_security_groups.default.ids
  }
}
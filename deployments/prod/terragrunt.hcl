locals {
  # Automatically load deployment variables
  deployment_vars = read_terragrunt_config(find_in_parent_folders("deployment.hcl"))
  account_id      = local.deployment_vars.locals.account_id

  # Automatically load area variables
  area_vars = read_terragrunt_config(find_in_parent_folders("area.hcl"))

  # Automatically load region variables
  region_vars = read_terragrunt_config(find_in_parent_folders("region.hcl"))
  aws_region  = local.region_vars.locals.aws_region

  # Get endpoint overrides
  aws_endpoints   = lookup(local.deployment_vars.locals, "aws_endpoints", {})

  deployment_tf_state_bucket = local.deployment_vars.locals.tf_state_bucket
  region_tf_state_bucket = local.deployment_vars.locals.aws_region
}

remote_state {
  backend = "s3"
  generate = {
    path      = "backend.tf"
    if_exists = "overwrite_terragrunt"
  }
  config = {
    # Deployment controls the Terraform state bucket
    bucket         = "${local.deployment_tf_state_bucket}"
    # key reflects folder heirachy
    key            = "${path_relative_to_include()}/terraform.tfstate"
    region         = local.region_tf_state_bucket
    encrypt        = true
    dynamodb_table = "${local.deployment_tf_state_bucket}-lock"
  }
}

terraform_version_constraint  = ">=1.0.10"
terragrunt_version_constraint = ">=0.35.8"

# Generate an AWS provider block
generate "provider" {
  path      = "provider.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
# Uncomment this block to support depricated resources, which are removed in AWS Provider 5.x
# terraform {
#   required_providers {
#     aws = {
#       source  = "hashicorp/aws"
#       version = "~> 4.66.0"
#     }
#   }
# }
provider "aws" {
  region              = "${local.aws_region}"
  # Only these AWS Account IDs may be operated on by this template
  allowed_account_ids = ["${local.account_id}"]
}
EOF
}

# These variables are passed as inputs to all leaf terragrunt.hcl
inputs = merge(
  local.deployment_vars.locals,
  local.area_vars.locals,
  local.region_vars.locals,
)

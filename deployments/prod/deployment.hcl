# Set Deployment variables
locals {
  # Ensure this is only ran against the expected account
  account_id        = "************"  # Populate with actual AWS account ID
  # domain            = ""  # Domain for deployment 
  mode              = "dev"
  organization      = "Entanglement"
  tf_state_bucket   = "terraform-baiker-${local.mode}"
  bucket_prefix     = "baiker-"
  bucket_domain     = replace(local.domain, ".", "-")

  ami_account_ids   = []  #  accounts owning AMIs

  office_cidrs = [
  ]

  # ===================================
  # === CUSTOMISE FROM HERE ONWARDS ===
  # ===================================

  # # DNS Names and resources
  deployment          = "baiker"
  deployment_name     = local.deployment
  vpc_cidr            = "" # set vpc cidr
  aws_region          = "ap-northeast-1"

  lambda_deployment_path  = "/tmp"
  ecr_repo_name           = "baiker"
  ecr_image_tag           = "latest"

  config = {
    aws-init = {
      ap-northeast-1 = {
        vpc = {
          deployment_name  = "${local.deployment}-${local.mode}-vpc"    #set vpc and subnet
          create_jumphost  = true    # set jumphost
        }
        # vpc-instance = {
        #   source_ref        = local.source_ref
        #   is_public         = true
        #   instance_type     = "t2.micro"
        #   instance_name     = ""
        #   ami_id            = "ami-07e05dd5fe4a520fb"
        # }
      }
    }

    aws-utilities = {
      ap-northeast-1 = {
        #cognito
        #apigateway
        #lambda sync cognito to database
      }
    }

    aws-services = {
      ap-northeast-1 = {
        #ecs
        #ec2
        #database
        #redis
      }
    }

  }
}

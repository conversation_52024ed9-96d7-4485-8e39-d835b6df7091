terraform {
  source = "../../../../..//terraform/lambda/lambda-sync-cognito-database"
}


include {
  path = find_in_parent_folders()
}

locals {
  module_name     = reverse(split("/", get_terragrunt_dir()))[0]
  deployment_vars = read_terragrunt_config(find_in_parent_folders("deployment.hcl"))

  area_vars = read_terragrunt_config(find_in_parent_folders("area.hcl"))
  area      = local.area_vars.locals.area


  region_vars = read_terragrunt_config(find_in_parent_folders("region.hcl"))
  aws_region  = local.region_vars.locals.aws_region
  dir_region  = local.region_vars.locals.dir_region

  module_vars = local.deployment_vars.locals.config[local.area][local.dir_region][local.module_name]
}


# dependency "vpc" {
#   config_path = "../../../../aws-init/us-east-1/vpc"
# }

# dependency "rds" {
#   config_path = "../../../../aws-utilities/us-east-1/rds"
# }

# dependency "cognito" {
#   config_path = "../../../../aws-utilities/us-east-1/cognito"
# }

inputs = merge(
  {
    vpc_id = "vpc-0e707d4ae47274017"#dependency.vpc.outputs.vpc_id
    db_host = "baiker-db-instance-1.c7q0zq077000.us-east-1.rds.amazonaws.com" #dependency.rds.outputs.db_host
    db_port = "5432" #dependency.rds.outputs.db_port
    db_user = "postgres" #dependency.rds.outputs.db_user
    db_password = "Engtanglement123x$X" #dependency.rds.outputs.db_password
    db_name = "entanglement" #dependency.rds.outputs.db_name
  },
try(local.module_vars, {}))


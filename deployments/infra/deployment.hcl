# Set Deployment variables
locals {
  # Ensure this is only ran against the expected account
  account_id        = "************"  # Populate with actual AWS account ID
  # domain            = ""  # Domain for deployment 
  environment       = "infra"
  deployment        = "baiker"
  organization      = "Entanglement"
  aws_region        = "us-east-1"
  global_region     = "us-east-1"
  tf_state_bucket   = "${local.deployment}-terraform-state-${local.environment}"

  ami_account_ids   = []  #  accounts owning AMIs

  office_cidrs = [
  ]

  # ===================================
  # === CUSTOMISE FROM HERE ONWARDS ===
  # ===================================

  # # DNS Names and resources

  vpc_cidr            = "" # set vpc cidr

  lambda_deployment_path  = "./"
  ecr_repo_name           = "baiker"
  ecr_image_tag           = "latest"

  config = {
    aws-init = {
      us-east-1 = {
        # vpc = {
        #   deployment_name  = "${local.deployment}-${local.mode}-vpc"    #set vpc and subnet
        #   create_jumphost  = true    # set jumphost
        # }
        # vpc-instance = {
        #   source_ref        = local.source_ref
        #   is_public         = true
        #   instance_type     = "t2.micro"
        #   instance_name     = ""
        #   ami_id            = "ami-07e05dd5fe4a520fb"
        # }
      }
    }

    aws-utilities = {
      us-east-1 = {
        #cognito
        #apigateway
        lambda-sync-cognito-database = {
          function_name = "lambda-sync-cognito-database"
        }
        #lambda sync cognito to database
      }
    }

    aws-services = {
      us-east-1 = {
        #ecs
        #ec2
        #database
        #redis
      }
    }

  }
}
